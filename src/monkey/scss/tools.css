:root {
	--tabbar-item-margin-bottom: 2px;
	--primary-blue: #3e8bf7;
	--primary-blue-light: #52a6f8;
	--primary-blue-dark: #2563eb;
	--card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	--card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	--gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	--gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	--gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

body {
	width: 100vw;
	height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 美化主要按钮样式 */
.cu-btn-style {
	@apply w-[690rpx] flex items-center justify-center rounded-2xl !text-white text-sm !py-4 tracking-widest font-semibold transition-all duration-300 transform;
	background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
	box-shadow: var(--card-shadow);
	border: none;
}

.cu-btn-style:hover {
	transform: translateY(-2px);
	box-shadow: var(--card-shadow-hover);
}

.cu-btn-style:active {
	transform: translateY(0);
	background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
}

/* 美化取消按钮样式 */
.cu-cancel-btn {
	@apply w-[690rpx] flex items-center justify-center rounded-2xl !text-white text-sm !py-4 tracking-widest font-semibold transition-all duration-300 transform;
	background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
	box-shadow: var(--card-shadow);
	border: none;
}

.cu-cancel-btn:hover {
	transform: translateY(-2px);
	box-shadow: var(--card-shadow-hover);
}

.cu-cancel-btn:active {
	transform: translateY(0);
	background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* 主题渐变背景 */
.bg-gradient-to-b-theme-blue {
	background: linear-gradient(180deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
}

/* 新增美化样式 */
.card-enhanced {
	@apply bg-white rounded-2xl border border-gray-100 transition-all duration-300 transform;
	box-shadow: var(--card-shadow);
}

.card-enhanced:hover {
	transform: translateY(-4px);
	box-shadow: var(--card-shadow-hover);
	border-color: rgba(62, 139, 247, 0.2);
}

.card-interactive {
	@apply transition-all duration-200 transform;
}

.card-interactive:active {
	transform: scale(0.98);
}

/* 渐变文字效果 */
.text-gradient-primary {
	background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* 美化滚动条 */
::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

::-webkit-scrollbar-thumb {
	background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--primary-blue-dark);
}