<template>
  <van-popup v-model:show="show" :position="position" @click-overlay="handleMaskClick" round>
    <div v-if="title" class="px-4 py-3 border-b border-gray-200 text-base font-bold text-gray-800 mb-4 flex items-center justify-between gap-2">
      <text>{{ title }}</text>
      <text class="i-mdi-close text-gray-500 text-base" @click="close"></text>
    </div>
    <slot></slot>
  </van-popup>
</template>
<script setup lang="ts">
  const props = defineProps<{
    show: boolean;
    title: string;
    position: 'bottom' | 'top' | 'left' | 'right';
  }>();

  const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'update:show', value: boolean): void;
  }>();

  const close = () => {
    emit('update:show', false);
    emit('close');
  };

  const handleMaskClick = () => {
    close();
  };
</script>
