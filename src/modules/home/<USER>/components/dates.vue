<template>
  <view class="grid items-center" :class="`grid-cols-${cols} gap-${gap}`">
    <view
      v-for="(date, index) in currentDates"
      :key="date.date"
      @click="selectDate(date, index)"
      :class="['flex-shrink-0 rounded-lg p-3 transition-all duration-300 relative', selectedDateIndex === index ? 'bg-theme-blue-600 text-white shadow-md transform scale-102' : 'bg-gray-50 text-gray-700 border border-gray-200 active:bg-gray-100']"
    >
      <view class="flex flex-col items-center">
        <text :class="['text-xs mb-1', selectedDateIndex === index ? 'text-blue-100' : 'text-gray-500']">
          {{ date.week }}
        </text>
        <text :class="['text-xs font-medium', selectedDateIndex === index ? 'text-white' : 'text-gray-800']">
          {{ date.date }}
        </text>
      </view>
    </view>

    <!-- More button -->
    <view v-if="isMore" class="flex-shrink-0 rounded-lg p-3 w-16 bg-blue-50 text-blue-700 border border-blue-200 active:bg-blue-100" @click="onMoreClick">
      <view class="flex flex-col items-center">
        <text class="text-xs mb-1 text-blue-500">更多</text>
        <text class="text-xs font-medium text-blue-800">...</text>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
  import { HomeTypes } from '@/monkey/types';
  const props = withDefaults(defineProps<{
    currentDates: HomeTypes.InboundDateItem[];
    selectedDateIndex: number;
    isMore: boolean;
    cols: number;
    gap: number;
  }>(), {
    isMore: true,
    cols: 5,
    gap: 1.5,
  });

  const emit = defineEmits<{
    (e: 'selectDate', date: HomeTypes.InboundDateItem, index: number): void;
    (e: 'more'): void;
  }>();

  const selectDate = (date: HomeTypes.InboundDateItem, index: number) => {
    emit('selectDate', date, index);
  };

  const onMoreClick = () => {
    emit('more');
  };
</script>
